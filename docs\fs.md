# fafafa.core.fs 文件系统模块 - C API 风格设计

> **设计原则**: 简单直接的 C API 风格，提供实用的文件系统操作函数

本文档规划 `fafafa.core.fs` 模块，采用 C API 风格的函数设计，简单易用，性能优先。

---

## 🎯 设计目标

### 1. **C API 风格**
- 简单的函数调用，无复杂的类层次
- 直接的参数传递和返回值
- 最小化的抽象层

### 2. **实用性优先**
- 基于 FreePascal RTL 的薄封装
- 统一的错误码返回
- 常用操作的便捷函数

### 3. **性能导向**
- 最小的函数调用开销
- 直接的内存操作
- 可选的安全检查

---

## 🏗️ 模块架构设计

### 核心单元 (扁平化命名)

```
src/
├── fafafa.core.fs.path.pas       # 路径操作工具类
├── fafafa.core.fs.file.pas       # 文件操作封装
├── fafafa.core.fs.directory.pas  # 目录操作封装
├── fafafa.core.fs.stream.pas     # 流式文件处理
├── fafafa.core.fs.utils.pas      # 文件系统工具函数
└── fafafa.core.fs.pas            # 主入口单元 (可选)
```

---

## 📋 实施计划

### 阶段一: 路径操作工具 (1 周)

**目标**: 提供比 RTL 更好用的路径操作 API

#### 1.1 TPath 静态工具类

```pascal
// fafafa.core.fs.path.pas
unit fafafa.core.fs.path;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, fafafa.core.base;

type
  { TPath - 路径操作工具类 }
  TPath = class abstract
  public
    { 路径组合和分解 }
    class function Combine(const aPath1, aPath2: string): string; overload;
    class function Combine(const aPaths: array of string): string; overload;
    class function GetFileName(const aPath: string): string;
    class function GetFileNameWithoutExtension(const aPath: string): string;
    class function GetExtension(const aPath: string): string;
    class function GetDirectoryName(const aPath: string): string;
    class function GetFullPath(const aPath: string): string;

    { 路径验证 }
    class function IsAbsolute(const aPath: string): Boolean;
    class function IsRelative(const aPath: string): Boolean;
    class function IsValidPath(const aPath: string): Boolean;
    class function IsValidFileName(const aFileName: string): Boolean;

    { 路径规范化 }
    class function Normalize(const aPath: string): string;
    class function GetRelativePath(const aFromPath, aToPath: string): string;

    { 平台相关 }
    class function GetTempPath: string;
    class function GetCurrentDirectory: string;
    class function GetHomeDirectory: string;

    { 路径比较 }
    class function PathsEqual(const aPath1, aPath2: string): Boolean;

    { 常量 }
    class function DirectorySeparator: Char;
    class function AltDirectorySeparator: Char;
    class function PathSeparator: Char;
  end;
```

#### 1.2 路径安全检查

```pascal
type
  TPathValidationResult = record
    IsValid: Boolean;
    ErrorMessage: string;

    class function Valid: TPathValidationResult; static;
    class function Invalid(const aMessage: string): TPathValidationResult; static;
  end;

  TPathValidator = class abstract
  public
    { 安全检查 }
    class function ValidatePath(const aPath: string): TPathValidationResult;
    class function ValidateFileName(const aFileName: string): TPathValidationResult;
    class function CheckPathTraversal(const aPath: string): Boolean;
    class function SanitizePath(const aPath: string): string;
  end;
```

### 阶段二: 文件操作封装 (1 周)

**目标**: 提供比 RTL 更安全、更易用的文件操作 API

#### 2.1 TFile 文件操作类

```pascal
// fafafa.core.fs.file.pas
unit fafafa.core.fs.file;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  Classes, SysUtils, fafafa.core.base, fafafa.core.fs.path;

type
  { 文件操作结果 }
  TFileOperationResult = record
    Success: Boolean;
    ErrorCode: Integer;
    ErrorMessage: string;
    BytesProcessed: Int64;

    class function Ok(aBytesProcessed: Int64 = 0): TFileOperationResult; static;
    class function Error(aCode: Integer; const aMessage: string): TFileOperationResult; static;
  end;

  { TFile - 文件操作封装类 }
  TFile = class abstract
  public
    { 文本文件操作 }
    class function ReadAllText(const aPath: string): string; overload;
    class function ReadAllText(const aPath: string; out aResult: string): TFileOperationResult; overload;
    class function WriteAllText(const aPath, aContent: string): TFileOperationResult;
    class function AppendAllText(const aPath, aContent: string): TFileOperationResult;

    { 二进制文件操作 }
    class function ReadAllBytes(const aPath: string): TBytes; overload;
    class function ReadAllBytes(const aPath: string; out aResult: TBytes): TFileOperationResult; overload;
    class function WriteAllBytes(const aPath: string; const aData: TBytes): TFileOperationResult;

    { 流式操作 }
    class function OpenRead(const aPath: string): TFileStream;
    class function OpenWrite(const aPath: string): TFileStream;
    class function OpenAppend(const aPath: string): TFileStream;

    { 文件信息 }
    class function Exists(const aPath: string): Boolean;
    class function GetSize(const aPath: string): Int64;
    class function GetModificationTime(const aPath: string): TDateTime;
    class function GetCreationTime(const aPath: string): TDateTime;

    { 文件操作 }
    class function Copy(const aSource, aDest: string; aOverwrite: Boolean = False): TFileOperationResult;
    class function Move(const aSource, aDest: string): TFileOperationResult;
    class function Delete(const aPath: string): TFileOperationResult;

    { 安全操作 }
    class function SafeCopy(const aSource, aDest: string; aOverwrite: Boolean = False): TFileOperationResult;
    class function AtomicWrite(const aPath, aContent: string): TFileOperationResult;
  end;
```

#### 2.2 大文件处理

```pascal
type
  { 大文件流式处理 }
  TLargeFileReader = class
  private
    FStream: TFileStream;
    FBufferSize: Integer;
    FBuffer: TBytes;
  public
    constructor Create(const aPath: string; aBufferSize: Integer = 64 * 1024);
    destructor Destroy; override;

    function ReadChunk(out aData: TBytes): Boolean;
    function ReadLine(out aLine: string): Boolean;

    property Position: Int64 read GetPosition write SetPosition;
    property Size: Int64 read GetSize;
  end;

  TLargeFileWriter = class
  private
    FStream: TFileStream;
    FBufferSize: Integer;
    FBuffer: TBytes;
    FBufferPos: Integer;
  public
    constructor Create(const aPath: string; aBufferSize: Integer = 64 * 1024);
    destructor Destroy; override;

    procedure WriteChunk(const aData: TBytes);
    procedure WriteLine(const aLine: string);
    procedure Flush;

    property Position: Int64 read GetPosition;
  end;
```

### 阶段三: 目录操作封装 (1 周)

**目标**: 提供强大的目录操作和文件枚举功能

#### 3.1 TDirectory 目录操作类

```pascal
// fafafa.core.fs.directory.pas
unit fafafa.core.fs.directory;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  Classes, SysUtils, fafafa.core.base, fafafa.core.fs.path;

type
  { 目录枚举选项 }
  TDirectoryEnumOptions = set of (
    deoRecursive,           // 递归枚举子目录
    deoIncludeDirectories,  // 包含目录
    deoIncludeFiles,        // 包含文件
    deoIncludeHidden        // 包含隐藏文件
  );

  { 文件信息记录 }
  TFileInfo = record
    Name: string;
    FullPath: string;
    Size: Int64;
    IsDirectory: Boolean;
    IsHidden: Boolean;
    CreationTime: TDateTime;
    ModificationTime: TDateTime;
    AccessTime: TDateTime;
  end;

  { TDirectory - 目录操作封装类 }
  TDirectory = class abstract
  public
    { 目录基本操作 }
    class function Exists(const aPath: string): Boolean;
    class function Create(const aPath: string): TFileOperationResult;
    class function CreateRecursive(const aPath: string): TFileOperationResult;
    class function Delete(const aPath: string; aRecursive: Boolean = False): TFileOperationResult;
    class function Move(const aSource, aDest: string): TFileOperationResult;
    class function Copy(const aSource, aDest: string; aRecursive: Boolean = True): TFileOperationResult;

    { 目录枚举 }
    class function EnumerateFiles(const aPath: string; const aPattern: string = '*'): TArray<string>; overload;
    class function EnumerateFiles(const aPath: string; aOptions: TDirectoryEnumOptions): TArray<TFileInfo>; overload;
    class function EnumerateDirectories(const aPath: string; const aPattern: string = '*'): TArray<string>;

    { 目录信息 }
    class function GetSize(const aPath: string; aRecursive: Boolean = True): Int64;
    class function GetFileCount(const aPath: string; aRecursive: Boolean = True): Integer;
    class function IsEmpty(const aPath: string): Boolean;

    { 实用工具 }
    class function GetCurrentDirectory: string;
    class function SetCurrentDirectory(const aPath: string): Boolean;
    class function GetTempDirectory: string;
    class function CreateTempDirectory(const aPrefix: string = 'tmp'): string;
  end;
```

#### 3.2 文件搜索和过滤

```pascal
type
  { 文件搜索条件 }
  TFileSearchCriteria = record
    Pattern: string;              // 文件名模式
    MinSize: Int64;              // 最小文件大小
    MaxSize: Int64;              // 最大文件大小
    CreatedAfter: TDateTime;     // 创建时间之后
    CreatedBefore: TDateTime;    // 创建时间之前
    ModifiedAfter: TDateTime;    // 修改时间之后
    ModifiedBefore: TDateTime;   // 修改时间之前
    Extensions: TArray<string>;  // 允许的扩展名

    class function Default: TFileSearchCriteria; static;
  end;

  { 文件搜索器 }
  TFileSearcher = class
  private
    FCriteria: TFileSearchCriteria;
    FResults: TArray<TFileInfo>;
  public
    constructor Create(const aCriteria: TFileSearchCriteria);

    function Search(const aRootPath: string): TArray<TFileInfo>;
    function SearchAsync(const aRootPath: string; aCallback: TProc<TFileInfo>): Boolean;

    property Criteria: TFileSearchCriteria read FCriteria write FCriteria;
  end;
```

### 阶段四: 文件系统工具 (1 周)

**目标**: 提供实用的文件系统工具和辅助功能

#### 4.1 文件比较和同步

```pascal
// fafafa.core.fs.utils.pas
unit fafafa.core.fs.utils;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  Classes, SysUtils, fafafa.core.base;

type
  { 文件比较结果 }
  TFileCompareResult = (
    fcrIdentical,     // 完全相同
    fcrDifferent,     // 内容不同
    fcrSizesDiffer,   // 大小不同
    fcrTimesDiffer,   // 时间不同
    fcrError          // 比较出错
  );

  { TFileUtils - 文件系统工具类 }
  TFileUtils = class abstract
  public
    { 文件比较 }
    class function CompareFiles(const aFile1, aFile2: string): TFileCompareResult;
    class function CompareFilesByHash(const aFile1, aFile2: string): Boolean;
    class function CalculateFileHash(const aPath: string): string;

    { 文件备份 }
    class function CreateBackup(const aPath: string; const aBackupSuffix: string = '.bak'): TFileOperationResult;
    class function RestoreBackup(const aPath: string; const aBackupSuffix: string = '.bak'): TFileOperationResult;

    { 临时文件 }
    class function CreateTempFile(const aPrefix: string = 'tmp'; const aExtension: string = '.tmp'): string;
    class function CreateTempFileInDir(const aDir, aPrefix: string): string;

    { 文件锁定 }
    class function LockFile(const aPath: string): Boolean;
    class function UnlockFile(const aPath: string): Boolean;
    class function IsFileLocked(const aPath: string): Boolean;

    { 磁盘空间 }
    class function GetDiskFreeSpace(const aPath: string): Int64;
    class function GetDiskTotalSpace(const aPath: string): Int64;
    class function GetDiskUsage(const aPath: string): Double; // 返回使用率 0.0-1.0
  end;
```

---

## 💡 使用示例

### 基础文件操作

```pascal
program FileOperationExample;

uses
  fafafa.core.fs.path,
  fafafa.core.fs.file,
  fafafa.core.fs.directory;

var
  LContent: string;
  LResult: TFileOperationResult;
  LFiles: TArray<string>;
begin
  // 路径操作
  var LPath := TPath.Combine(['C:', 'Users', 'Documents', 'test.txt']);
  WriteLn('Full path: ', TPath.GetFullPath(LPath));
  WriteLn('Directory: ', TPath.GetDirectoryName(LPath));
  WriteLn('Filename: ', TPath.GetFileName(LPath));

  // 文件读写
  LResult := TFile.WriteAllText(LPath, 'Hello, World!');
  if LResult.Success then
  begin
    LResult := TFile.ReadAllText(LPath, LContent);
    if LResult.Success then
      WriteLn('Content: ', LContent);
  end;

  // 目录操作
  LFiles := TDirectory.EnumerateFiles('C:\Temp', '*.txt');
  WriteLn('Found ', Length(LFiles), ' text files');

  // 安全操作
  if TFile.Exists(LPath) then
  begin
    LResult := TFile.Delete(LPath);
    if not LResult.Success then
      WriteLn('Delete failed: ', LResult.ErrorMessage);
  end;
end.
```

### 大文件处理

```pascal
procedure ProcessLargeFile(const aInputPath, aOutputPath: string);
var
  LReader: TLargeFileReader;
  LWriter: TLargeFileWriter;
  LChunk: TBytes;
begin
  LReader := TLargeFileReader.Create(aInputPath, 1024 * 1024); // 1MB 缓冲区
  try
    LWriter := TLargeFileWriter.Create(aOutputPath, 1024 * 1024);
    try
      while LReader.ReadChunk(LChunk) do
      begin
        // 处理数据块
        ProcessChunk(LChunk);
        LWriter.WriteChunk(LChunk);
      end;
    finally
      LWriter.Free;
    end;
  finally
    LReader.Free;
  end;
end;
```

### 文件搜索

```pascal
procedure SearchFiles;
var
  LCriteria: TFileSearchCriteria;
  LSearcher: TFileSearcher;
  LResults: TArray<TFileInfo>;
  LFile: TFileInfo;
begin
  // 设置搜索条件
  LCriteria := TFileSearchCriteria.Default;
  LCriteria.Pattern := '*.pas';
  LCriteria.MinSize := 1024;  // 至少 1KB
  LCriteria.CreatedAfter := Now - 30; // 30天内创建

  LSearcher := TFileSearcher.Create(LCriteria);
  try
    LResults := LSearcher.Search('C:\Projects');

    WriteLn('Found ', Length(LResults), ' Pascal files:');
    for LFile in LResults do
      WriteLn(Format('%s (%d bytes)', [LFile.Name, LFile.Size]));
  finally
    LSearcher.Free;
  end;
end;
```

---

## 🧪 测试计划

### 单元测试覆盖

```pascal
// tests/test_fs_path.pas
unit test_fs_path;

{$mode objfpc}{$H+}

interface

uses
  fpcunit, testregistry, fafafa.core.fs.path;

type
  TTestPath = class(TTestCase)
  published
    procedure Test_Combine_TwoPaths;
    procedure Test_Combine_MultiplePaths;
    procedure Test_GetFileName;
    procedure Test_GetExtension;
    procedure Test_GetDirectoryName;
    procedure Test_IsAbsolute;
    procedure Test_Normalize;
    procedure Test_PathValidation;
    procedure Test_PathTraversalSecurity;
  end;

// tests/test_fs_file.pas
unit test_fs_file;

{$mode objfpc}{$H+}

interface

uses
  fpcunit, testregistry, fafafa.core.fs.file;

type
  TTestFile = class(TTestCase)
  private
    FTempDir: string;
    FTestFile: string;
  protected
    procedure SetUp; override;
    procedure TearDown; override;
  published
    procedure Test_ReadWriteText;
    procedure Test_ReadWriteBytes;
    procedure Test_FileOperations;
    procedure Test_LargeFileHandling;
    procedure Test_AtomicOperations;
    procedure Test_ErrorHandling;
  end;
```

### 性能测试

```pascal
procedure BenchmarkFileOperations;
const
  TEST_FILE_SIZE = 100 * 1024 * 1024; // 100MB
  BUFFER_SIZES: array[0..3] of Integer = (4096, 16384, 65536, 262144);
var
  i: Integer;
  LStart, LEnd: TDateTime;
  LBuffer: TBytes;
begin
  // 测试不同缓冲区大小的性能
  for i := Low(BUFFER_SIZES) to High(BUFFER_SIZES) do
  begin
    LStart := Now;

    // 执行文件操作测试
    TestFileOperationWithBuffer(BUFFER_SIZES[i]);

    LEnd := Now;
    WriteLn(Format('Buffer size %d: %d ms',
      [BUFFER_SIZES[i], MilliSecondsBetween(LEnd, LStart)]));
  end;
end;
```

---

## 📋 实施优先级

### 第一优先级 (立即实施)
- ✅ TPath 路径操作工具类
- ✅ TFile 基础文件操作
- ✅ 错误处理机制

### 第二优先级 (近期实施)
- 🔄 TDirectory 目录操作
- 🔄 大文件流式处理
- 🔄 文件搜索功能

### 第三优先级 (后续实施)
- 📋 文件系统工具类
- 📋 性能优化
- 📋 跨平台兼容性测试

---

## 🎯 预期收益

### 开发效率提升
- 统一的 API 设计，减少学习成本
- 更好的错误处理，减少调试时间
- 丰富的工具函数，减少重复代码

### 代码质量提升
- 类型安全的操作
- 统一的错误处理机制
- 完善的单元测试覆盖

### 性能优化
- 大文件的流式处理
- 批量操作优化
- 内存使用优化

这个文件系统模块设计基于 FreePascal 的实际能力，提供实用、高效的文件系统操作接口，可以显著提升文件操作的开发效率和代码质量。
```
