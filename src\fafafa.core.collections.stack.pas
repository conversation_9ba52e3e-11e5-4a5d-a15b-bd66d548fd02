unit fafafa.core.collections.stack;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  fafafa.core.collections.base;

type

  { IStack 泛型栈接口 }
  generic IStack<T> = interface(specialize IGenericCollection<T>)
  ['{b2d0130d-760b-4369-86c8-4ccd5ddac18c}']

    {**
     * Push
     *
     * @desc 从内存指针压入多个元素(拷贝)
     *
     * @params
     *   aSrc          指针
     *   aElementCount 元素数量
     *
     * @remark 指针内存应为泛型元素数组内存
     *
     * @complexity O(n) 其中 n 为 aElementCount
     *
     * @exceptions
     *   EArgumentNil     aSrc 为 nil
     *   EOutOfMemory     内存不足
     *}
    procedure Push(const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * Push
     *
     * @desc 从数组压入元素
     *
     * @params
     *   aSrc 要压入的元素数组
     *
     * @complexity O(n) 其中 n 为数组长度
     *
     * @exceptions
     *   EOutOfMemory     内存不足
     *}
    procedure Push(const aSrc: array of T); overload;

    {**
     * Push
     *
     * @desc 从容器压入指定数量的元素(拷贝)
     *
     * @params
     *   aSrc          要添加的源容器
     *   aElementCount 要添加的元素数量
     *
     * @complexity O(n) 其中 n 为 aElementCount
     *
     * @remark 如果容器为空会抛出异常
     *
     * @exceptions
     *   EArgumentNil     aSrc 为 nil
     *   EOutOfRange      aElementCount 超出源容器范围
     *   EOutOfMemory     内存不足
     *}
    procedure Push(const aSrc: TCollection; aElementCount: SizeUInt); overload;

    {**
     * Push
     *
     * @desc 压入单个元素到栈顶
     *
     * @params
     *   aElement 要压入栈顶的元素
     *
     * @complexity O(1) 平摊时间复杂度
     *
     * @remark 该方法将一个元素压入栈顶
     *
     * @exceptions
     *   EOutOfMemory     内存不足
     *}
    procedure Push(const aElement: T); overload;


    {**
     * TryPop
     *
     * @desc 尝试弹出多个元素到指定内存指针处
     *
     * @params
     *   aDst          目标内存指针
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @complexity O(n) 其中 n 为 aElementCount
     *
     * @remark
     *   需确保指针内存足够容纳指定数量的元素
     *   如果栈元素不足，将返回 False 且不修改 aDst
     *   成功时从栈顶开始弹出指定数量的元素
     *}
    function TryPop(aDst: Pointer; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPop
     *
     * @desc 尝试弹出多个元素拷贝到指定数组
     *
     * @params
     *   aDst          目标数组
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @complexity O(n) 其中 n 为 aElementCount
     *
     * @remark
     *   数组会被修改长度为指定大小
     *   如果栈元素不足，将返回 False 且不修改 aDst
     *   成功时从栈顶开始弹出指定数量的元素
     *}
    function TryPop(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPop
     *
     * @desc 尝试弹出栈顶元素到指定变量
     *
     * @params
     *   aDst 用于存储弹出元素的变量
     *
     * @return 返回是否成功
     *
     * @complexity O(1)
     *
     * @remark 如果栈为空，将返回 False 且不修改 aDst
     *}
    function TryPop(var aDst: T): Boolean; overload;

    {**
     * Pop
     *
     * @desc 弹出栈顶元素
     *
     * @return 返回弹出的栈顶元素
     *
     * @complexity O(1)
     *
     * @remark 如果栈为空，将抛出异常
     *
     * @exceptions
     *   EOutOfRange      栈为空时调用
     *}
    function  Pop: T; overload;


    {**
     * TryPeekCopy
     *
     * @desc 尝试拷贝栈顶多个元素到指定内存
     *
     * @params
     *   aDst          目标内存指针
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @complexity O(n) 其中 n 为 aElementCount
     *
     * @remark
     *   需确保指针内存足够容纳指定数量的元素
     *   如果栈元素不足，将返回 False 且不修改 aDst
     *   成功时从栈顶开始拷贝指定数量的元素，不修改栈内容
     *}
    function TryPeekCopy(aDst: Pointer; aElementCount: SizeUint): Boolean; overload;

    {**
     * TryPeek
     *
     * @desc 获取栈顶多个元素到指定数组
     *
     * @params
     *   aDst          目标数组
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @complexity O(n) 其中 n 为 aElementCount
     *
     * @remark
     *   数组会被修改长度为指定大小
     *   如果栈元素不足，将返回 False 且不修改 aDst
     *   成功时从栈顶开始拷贝指定数量的元素，不修改栈内容
     *}
    function TryPeek(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPeek
     *
     * @desc 尝试获取栈顶元素但不弹出
     *
     * @params
     *   aElement 用于存储栈顶元素的变量
     *
     * @return 返回是否成功
     *
     * @complexity O(1)
     *
     * @remark 如果栈为空,将返回 False 且不修改 aElement
     *}
    function  TryPeek(var aElement: T): Boolean; overload;

    {**
     * PeekRange
     *
     * @desc 获取从栈顶开始指定数量的元素的内存指针
     *
     * @params
     *   aElementCount 元素数量
     *
     * @return 返回指向栈顶元素的指针，失败时返回 nil
     *
     * @complexity O(1)
     *
     * @remark
     *   如果栈为空或者元素数量大于栈中元素数量会返回 nil
     *   返回的指针指向栈内部存储，不应修改其内容
     *   指针的有效性仅在栈结构未发生变化时保证
     *}
    function PeekRange(aElementCount: SizeUInt): specialize TGenericCollection<T>.PGenericPtr; overload;

    {**
     * Peek
     *
     * @desc 获取栈顶元素但不弹出
     *
     * @return 返回栈顶元素
     *
     * @complexity O(1)
     *
     * @remark 如果栈为空，将抛出异常
     *
     * @exceptions
     *   EOutOfRange      栈为空时调用
     *}
    function  Peek: T; overload;

    {**
     * 便利方法和别名
     *}

    {**
     * PushTop
     *
     * @desc Push 的语义化别名 - 压入元素到栈顶
     *
     * @params
     *   aElement 要压入栈顶的元素
     *
     * @complexity O(1) 平摊时间复杂度
     *}
    procedure PushTop(const aElement: T); {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}

    {**
     * PopTop
     *
     * @desc Pop 的语义化别名 - 弹出栈顶元素
     *
     * @return 返回弹出的栈顶元素
     *
     * @complexity O(1)
     *
     * @exceptions
     *   EOutOfRange      栈为空时调用
     *}
    function  PopTop: T; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}

    {**
     * PeekTop
     *
     * @desc Peek 的语义化别名 - 查看栈顶元素但不弹出
     *
     * @return 返回栈顶元素
     *
     * @complexity O(1)
     *
     * @exceptions
     *   EOutOfRange      栈为空时调用
     *}
    function  PeekTop: T; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}

    {**
     * TryPeekTop
     *
     * @desc TryPeek 的语义化别名 - 尝试查看栈顶元素
     *
     * @params
     *   aElement 用于存储栈顶元素的变量
     *
     * @return 返回是否成功
     *
     * @complexity O(1)
     *}
    function  TryPeekTop(var aElement: T): Boolean; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
  end;

  { TArrayStack 基于数组的栈实现 }
  generic TArrayStack<T> = class(specialize TGenericCollection<T>, specialize IStack<T>)
  private
    FArray: specialize TArray<T>;
  public
    constructor Create(aAllocator: TAllocator; aData: Pointer); override; overload;
    constructor Create; overload;
    constructor Create(aAllocator: TAllocator); overload;
    constructor Create(const aSrc: Pointer; aElementCount: SizeUInt); overload;
    constructor Create(const aSrc: array of T); overload;
    constructor Create(const aSrc: TCollection); overload;
    destructor  Destroy; override;

    { ICollection }
    function  PtrIter: TPtrIter; override;
    function  GetCount: SizeUInt; override;
    procedure Clear; override;
    procedure SerializeToArrayBuffer(aDst: Pointer; aCount: SizeUInt); override;
    procedure LoadFromUnChecked(const aSrc: Pointer; aCount: SizeUInt); override;
    procedure AppendUnChecked(const aSrc: Pointer; aCount: SizeUInt); override;
    procedure AppendToUnChecked(const aDst: TCollection); override;

    { IGenericCollection }
    procedure SaveToUnChecked(aDst: TCollection); override;

    { IStack }
    procedure Push(const aSrc: Pointer; aElementCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
    procedure Push(const aSrc: array of T); overload; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
    procedure Push(const aSrc: TCollection; aElementCount: SizeUInt); overload; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
    procedure Push(const aElement: T); overload; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}

    function TryPop(aDst: Pointer; aElementCount: SizeUInt): Boolean; overload;
    function TryPop(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;
    function TryPop(var aDst: T): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
    function Pop: T; overload; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}

    function TryPeekCopy(aDst: Pointer; aElementCount: SizeUint): Boolean; overload;
    function TryPeek(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;
    function TryPeek(var aElement: T): Boolean; overload; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
    function PeekRange(aElementCount: SizeUInt): specialize TGenericCollection<T>.PGenericPtr; overload; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
    function Peek: T; overload; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}

    { 别名方法 }
    procedure PushTop(const aElement: T); {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
    function  PopTop: T; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
    function  PeekTop: T; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}
    function  TryPeekTop(var aElement: T): Boolean; {$IFDEF FAFAFA_CORE_INLINE}inline;{$ENDIF}

    { 属性 }
    property Items: specialize TArray<T> read FArray;
  end;

  { 数组栈工厂函数 }

  generic function MakeArrayStack<T>: specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeArrayStack<T>(const aSrc: Pointer; aElementCount: SizeUInt): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeArrayStack<T>(const aSrc: array of T): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: array of T; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: array of T; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeArrayStack<T>(const aSrc: TCollection): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: TCollection; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: TCollection; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeArrayStack<T>(const aSrc: TCollection; aElementCount: SizeUInt): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  { 链表栈 }

  generic function MakeLinkedStack<T>: specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeLinkedStack<T>(const aSrc: Pointer; aElementCount: SizeUInt): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeLinkedStack<T>(const aSrc: array of T): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: array of T; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: array of T; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeLinkedStack<T>(const aSrc: TCollection): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: TCollection; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: TCollection; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeLinkedStack<T>(const aSrc: TCollection; aElementCount: SizeUInt): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

implementation

uses
  fafafa.core.collections.arr;

generic function MakeArrayStack<T>: specialize IStack<T>;
begin
  
end;

generic function MakeArrayStack<T>(aAllocator: TAllocator): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(aAllocator: TAllocator; aData: Pointer): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: Pointer; aElementCount: SizeUInt): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: array of T): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: array of T; aAllocator: TAllocator): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: array of T; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: TCollection): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: TCollection; aAllocator: TAllocator): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: TCollection; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: TCollection; aElementCount: SizeUInt): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>;
begin

end;


generic function MakeLinkedStack<T>: specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(aAllocator: TAllocator): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: Pointer; aElementCount: SizeUInt): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: array of T): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: array of T; aAllocator: TAllocator): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: array of T; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: TCollection): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: TCollection; aAllocator: TAllocator): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: TCollection; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: TCollection; aElementCount: SizeUInt): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;
begin

end;




end.