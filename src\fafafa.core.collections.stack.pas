unit fafafa.core.collections.stack;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  fafafa.core.collections.base;

type

  { IStack 泛型栈接口 }
  generic IStack<T> = interface(specialize IGenericCollection<T>)
  ['{b2d0130d-760b-4369-86c8-4ccd5ddac18c}']

    {**
     * Push
     *
     * @desc 从内存指针压入多个元素(拷贝)
     *
     * @params
     *   aSrc          指针
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @remark 指针内存应为泛型元素数组内存
     *}
    procedure Push(const aSrc: Pointer; aElementCount: SizeUInt); overload;

    {**
     * Push
     *
     * @desc 从数组压入元素
     *
     * @params
     *   aSrc 要压入的元素数组
     *}
    procedure Push(const aSrc: array of T); overload;

    {**
     * Push
     *
     * @desc 在末尾添加容器指定数量的元素(拷贝)
     *
     * @params
     *   aSrc          要添加的源容器
     *   aElementCount 要添加的元素数量
     *
     *
     * @remark 如果容器为空会抛出异常
     *}
    procedure Push(const aSrc: TCollection; aElementCount: SizeUInt); overload;

    {**
     * Push
     *
     * @desc 压入元素
     *
     * @params
     *   aElement 要压入栈顶的元素
     *
     * @remark 该方法将一个元素压入栈顶
     *}
    procedure Push(const aElement: T); overload;


    {**
     * TryPop
     *
     * @desc 尝试弹出多个元素到指定内存指针处
     *
     * @params
     *   aDst          目标内存指针
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @remark
     *   需确保指针内存足够容纳指定数量的元素
     *   如果栈为空，将返回 False 且不修改 aDst
     *}
    function TryPop(aDst: Pointer; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPop
     *
     * @desc 尝试弹出多个元素拷贝到指定数组
     *
     * @params
     *   aDst          目标数组
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @remark
     *   数组会被修改长度为指定大小
     *   如果栈为空，将返回 False 且不修改 aDst
     *}
    function TryPop(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * TryPop
     *
     * @desc 尝试弹出栈顶元素到指定变量
     *
     * @params
     *   aDst 用于存储弹出元素的变量
     *
     * @return 返回是否成功
     *
     * @remark 如果栈为空，将返回 False 且不修改 aElement
     *}
    function TryPop(var aDst: T): Boolean; overload;

    {**
     * Pop
     *
     * @desc 弹出栈顶元素
     *
     * @return 返回弹出的栈顶元素
     *
     * @remark 如果栈为空，将抛出异常
     *}
    function  Pop: T; overload;


    {**
     * TryPeekCopy
     *
     * @desc 尝试拷贝栈顶多个元素到指定内存
     *
     * @params
     *   aDst          指针
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @remark 需确保指针内存足够容纳指定数量的元素
     *}
    function TryPeekCopy(aDst: Pointer; aElementCount: SizeUint): Boolean; overload;

    {**
     * TryPeek
     *
     * @desc 获取栈顶多个元素到指定数组
     *
     * @params
     *   aDst     元素数组
     *   aElementCount 元素数量
     *
     * @return 返回是否成功
     *
     * @remark 数组会被修改长度为指定大小
     *}
    function TryPeek(var aDst: specialize TGenericArray<T>; aElementCount: SizeUInt): Boolean; overload;

    {**
     * Peek
     *
     * @desc 尝试获取栈顶元素但不弹出
     *
     * @params
     *  - aElement 用于存储栈顶元素的变量
     *
     * @return 返回是否成功
     *
     * @remark 如果栈为空,将返回 False 且不修改 aElement
     *}
    function  TryPeek(var aElement: T): Boolean; overload;

    {**
     * PeekRange
     *
     * @desc 获取从栈顶开始指定数量的元素的指针(容器内的指针)
     *
     * @params
     *   aElementCount 元素数量
     *
     * @return 返回指针
     *
     * @remark 如果容器为空或者元素数量大于容器数量会返回 nil
     *}
    function PeekRange(aElementCount: SizeUInt): specialize TGenericCollection<T>.PGenericPtr; overload;

    {**
     * Peek
     *
     * @desc 获取栈顶元素但不弹出
     *
     * @return 返回栈顶元素
     *
     * @remark 如果栈为空，将抛出异常
     *}
    function  Peek: T; overload;
  end;

  { 数组栈 }

  generic function MakeArrayStack<T>: specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeArrayStack<T>(const aSrc: Pointer; aElementCount: SizeUInt): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeArrayStack<T>(const aSrc: array of T): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: array of T; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: array of T; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeArrayStack<T>(const aSrc: TCollection): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: TCollection; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: TCollection; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeArrayStack<T>(const aSrc: TCollection; aElementCount: SizeUInt): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeArrayStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  { 链表栈 }

  generic function MakeLinkedStack<T>: specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeLinkedStack<T>(const aSrc: Pointer; aElementCount: SizeUInt): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeLinkedStack<T>(const aSrc: array of T): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: array of T; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: array of T; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeLinkedStack<T>(const aSrc: TCollection): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: TCollection; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: TCollection; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

  generic function MakeLinkedStack<T>(const aSrc: TCollection; aElementCount: SizeUInt): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>; overload;
  generic function MakeLinkedStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;

implementation

uses
  fafafa.core.collections.arr;

generic function MakeArrayStack<T>: specialize IStack<T>;
begin
  
end;

generic function MakeArrayStack<T>(aAllocator: TAllocator): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(aAllocator: TAllocator; aData: Pointer): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: Pointer; aElementCount: SizeUInt): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: array of T): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: array of T; aAllocator: TAllocator): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: array of T; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: TCollection): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: TCollection; aAllocator: TAllocator): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: TCollection; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: TCollection; aElementCount: SizeUInt): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>;
begin

end;

generic function MakeArrayStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>;
begin

end;


generic function MakeLinkedStack<T>: specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(aAllocator: TAllocator): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: Pointer; aElementCount: SizeUInt): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: Pointer; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: array of T): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: array of T; aAllocator: TAllocator): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: array of T; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: TCollection): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: TCollection; aAllocator: TAllocator): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: TCollection; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: TCollection; aElementCount: SizeUInt): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator): specialize IStack<T>; overload;
begin

end;

generic function MakeLinkedStack<T>(const aSrc: TCollection; aElementCount: SizeUInt; aAllocator: TAllocator; aData: Pointer): specialize IStack<T>; overload;
begin

end;




end.