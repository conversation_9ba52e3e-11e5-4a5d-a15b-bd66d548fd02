unit fafafa.core.collections.vec;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, Classes,
  fafafa.core.collections.base,
  fafafa.core.collections.arr,
  fafafa.core.collections.stack,
  fafafa.core.mem.allocator;

type

  { IVec 向量接口 }
  generic IVec<T> = interface(specialize IArray<T>)
  ['{C205D988-F671-4E47-8573-9AF2C85AC749}']

    {**
     * GetCapacity
     *
     * @desc 获取向量的容量
     *
     * @return 返回向量的容量
     *}
    function GetCapacity: SizeUint;

    {**
     * SetCapacity
     *
     * @desc 设置向量的容量
     *
     * @params
     *   aCapacity 要设置的容量
     *
     * @remark 如果失败会抛出异常
     *}
    procedure SetCapacity(aCapacity: SizeUint);

    {**
     * GetGrowStrategy
     *
     * @desc 获取容器当前的容量增长策略.
     *
     * @return 返回向量的增长策略
     *
     * @remark
     *   增长策略决定了当容器容量不足需要扩容时, 其内部存储空间应如何扩展.
     *   这是一个影响性能和内存使用效率的关键参数.
     *}
    function GetGrowStrategy: TGrowthStrategy;

    {**
     * SetGrowStrategy
     *
     * @desc 设置容器的容量增长策略.
     *
     * @params
     *   aGrowStrategy 要设置的增长策略.
     *
     * @remark
     *   通过改变增长策略,可以调整容器在自动扩容时的行为,
     *   从而在内存使用和重新分配次数 (影响性能) 之间进行权衡.
     *
     *   如果设置为 nil,容器将恢复使用默认的 `TGoldenRatioGrowStrategy` (黄金比例增长) 增长策略.
     *   默认策略在内存占用与分配性能之间提供了较好的平衡，适用于大多数场景.
     *
     *   用户可创建自定义策略并设置到容器中，但该策略对象的生命周期由用户负责管理.
     *   框架内置的常用增长策略包括:
     *     TCustomGrowthStrategy    自定义回调增长策略.通过回调函数精细控制增长行为.
     *     TDoublingGrowStrategy    指数增长策略(容量 * 2).广泛用于大多数动态容器
     *     TFixedGrowStrategy       固定线性增长(每次 += fixedSize).内存利用率高,适用于定长批量增长场景.
     *     TFactorGrowStrategy      因子增长(容量 *= factor).可调整增长幅度.
     *     TPowerOfTwoGrowStrategy  容量扩展至不小于所需容量的最小 2 的幂次.适用于哈希表、位运算容器
     *     TGoldenRatioGrowStrategy 黄金比例增长(容量 *= 1.618).空间浪费小,适合高增长频率场景.这是框架默认的增长策略.
     *     TAlignedWrapperStrategy  对齐包装策略.可包裹任意增长策略,对齐容量至指定字节边界(需为 2 的幂),提升 CPU 预取效率.
     *     TExactGrowStrategy       精确增长策略.始终将容量扩展到恰好满足需求,不浪费空间.但分配频繁,除非对分配行为有严格控制,否则不推荐使用.
     *}
    procedure SetGrowStrategy(aGrowStrategy: TGrowthStrategy);

    {**
     * TryReserve
     *
     * @desc 尝试预留额外的容量 (Count + aAdditional)
     *
     * @params
     *   aAdditional 要预留的额外容量(增加的元素数量,该接口用于确保容量足够)
     *
     * @return 如果预留成功返回 True,否则返回 False
     *
     * @remark
     *   如果预留失败不会抛出异常,只是返回 False
     *   预留的空间可能大于请求的空间,因为会按照增长策略进行分配
     *   如果当前容量足够,不会进行任何操作
     *}
    function TryReserve(aAdditional: SizeUint): Boolean;

    {**
     * Reserve
     *
     * @desc 预留额外的容量
     *
     * @params
     *   aAdditional 要预留的额外容量
     *
     * @remark
     *   如果预留失败会抛出异常
     *   预留的空间可能大于请求的空间,因为会按照增长策略进行分配
     *   如果当前容量足够,不会进行任何操作
     *}
    procedure Reserve(aAdditional: SizeUint);

    {**
     * TryReserveExact
     *
     * @desc 尝试预留精确的容量
     *
     * @params
     *   aAdditional 要预留的精确容量
     *
     * @return 如果预留成功返回 True,否则返回 False
     *
     * @remark
     *   如果预留失败不会抛出异常,只是返回 False
     *   向量预留的空间等于请求的空间,不会按照增长策略进行分配
     *}
    function TryReserveExact(aAdditional: SizeUint): Boolean;

    {**
     * ReserveExact
     *
     * @desc 预留精确的容量
     *
     * @params
     *   aAdditional 要预留的精确容量
     *
     * @remark
     *   如果预留失败会抛出异常
     *   向量预留的容量空间等于请求的空间,不会按照增长策略进行分配
     *}
    procedure ReserveExact(aAdditional: SizeUint);

    {**
     * Shrink
     *
     * @desc 收缩向量容量到实际使用的大小,释放多余的内存空间。
     *
     * @remark
     *   如果收缩失败会抛出异常
     *   收缩后的容量等于当前元素数量
     *}
    procedure Shrink;

    {**
     * ShrinkTo
     *
     * @desc 收缩向量容量到指定大小,释放多余的内存空间。
     *
     * @params
     *   aCapacity 要收缩到的容量大小
     *
     * @remark
     *   如果收缩失败会抛出异常
     *   如果指定的容量小于当前元素数量则会抛出异常(因为会截断元素)
     *   如果指定收缩的容量大于当前容量,什么也不会发生
     *}
    procedure ShrinkTo(aCapacity: SizeUint);

    {**
     * Truncate
     *
     * @desc 截断向量到指定数量,丢弃截断的元素。
     *
     * @params
     *   aCount 要截断到的数量
     *
     * @remark
     *   如果指定的数量大于当前元素数量,不会进行任何操作
     *   不会释放内存空间(不影响容量),只是修改元素数量
     *   如果需要截断元素数量并缩减容量,请使用 Truncate + Shrink
     *}
    procedure Truncate(aCount: SizeUint);

    {**
     * ResizeExact
     *
     * @desc 精确重置向量大小
     *
     * @params
     *   aNewSize 要重置的元素空间和容量大小
     *
     * @remark
     *   此接口精确改变容器大小和容量,如果新大小小于当前大小,会丢弃多余元素
     *   与 Resize 不同, Resize 按照增长策略进行扩容,而 ResizeExact 会严格按照新大小设置容量
     *
     * @exceptions
     *   EAlloc 内存分配/调整失败.
     *}
    procedure ResizeExact(aNewSize: SizeUint);

    
  end;




implementation

end.