unit fafafa.core.collections.vec;

{$mode objfpc}{$H+}
{$I fafafa.core.settings.inc}

interface

uses
  SysUtils, Classes,
  fafafa.core.collections.base,
  fafafa.core.collections.arr,
  fafafa.core.collections.stack;

type

  { IGrowthStrategy 增长策略接口 }
  IGrowthStrategy = interface
  ['{FC9AB81C-B79D-425C-A453-2137C359CF83}']
    function GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
  end;


  { TGrowthStrategy 增长策略基类 }
  TGrowthStrategy = class(TInterfacedObject, IGrowthStrategy)
  protected
    function DoGetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; virtual; abstract;
  public
    function GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; virtual;
  end;

  TGrowthStrategyClass = class of TGrowthStrategy;


  { 增长策略回调 }
  TGrowFunc    = function (aCurrentSize, aRequiredSize: SizeUInt; aData: Pointer): SizeUInt;
  TGrowMethod  = function (aCurrentSize, aRequiredSize: SizeUInt): SizeUInt of object;
  {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
  TGrowRefFunc = reference to function (aCurrentSize, aRequiredSize: SizeUInt): SizeUInt;
  {$ENDIF}
  TGrowProxyMethod = function (aCurrentSize, aRequiredSize: SizeUInt): SizeUInt of object;

  { TCustomGrowthStrategy 自定义回调增长策略 }
  TCustomGrowthStrategy = class(TGrowthStrategy)
  private
    FData:        Pointer;
    FGrowFunc:    TGrowFunc;
    FGrowMethod:  TGrowMethod;
    FGrowRefFunc: TGrowRefFunc;
    FGrowProxy:   TGrowProxyMethod;
    function GetData: Pointer; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  protected
    function DoGetGrowSizeFunc(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    function DoGetGrowSizeMethod(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    function DoGetGrowSizeRefFunc(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
    {$ENDIF}
    function DoGetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; override;
  public
    constructor Create(aGrowFunc: TGrowFunc; aData: Pointer);
    constructor Create(aGrowMethod: TGrowMethod; aData: Pointer);
    {$IFDEF FAFAFA_COLLECTIONS_ANONYMOUS_REFERENCES}
    constructor Create(aGrowRefFunc: TGrowRefFunc);
    {$ENDIF}

    property Data:     Pointer read GetData;
  end;

  { TCalcGrowStrategy 计算增长策略(这是个抽象类,不能直接使用) }
  TCalcGrowStrategy = class(TGrowthStrategy)
  protected
    function DoCalc(aCurrentSize: SizeUInt): SizeUInt; virtual; abstract;
    function DoGetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; override;
  public
  end;

  { TDoublingGrowStrategy 指数增长 }
  TDoublingGrowStrategy = class(TCalcGrowStrategy)
  protected
    function DoCalc(aCurrentSize: SizeUInt): SizeUInt; override;
  private
    class var FGlobal: TDoublingGrowStrategy;
    class destructor Destroy;
  public
    class function GetGlobal: TDoublingGrowStrategy; static; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  end;

  { TFixedGrowStrategy 固定线性增长 }
  TFixedGrowStrategy = class(TCalcGrowStrategy)
  private
    FFixedSize: SizeUInt;
    function GetFixedSize: SizeUInt; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  protected
    function DoCalc(aCurrentSize: SizeUInt): SizeUInt; override;
  public
    constructor Create(aFixedSize: SizeUInt);

    property FixedSize: SizeUInt read GetFixedSize;
  end;

  { TFactorGrowStrategy 因子增长 }
  TFactorGrowStrategy = class(TCalcGrowStrategy)
  private
    FFactor: Single;
    function GetFactor: Single; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  protected
    function DoCalc(aCurrentSize: SizeUInt): SizeUInt; override;
  public
    constructor Create(aFactor: Single);

    property Factor: Single read GetFactor;
  end;

  { TPowerOfTwoGrowStrategy 最近的2次幂增长 }
  TPowerOfTwoGrowStrategy = class(TGrowthStrategy)
  private
    class var FGlobal: TPowerOfTwoGrowStrategy;
    class destructor Destroy;
  public
    class function GetGlobal: TPowerOfTwoGrowStrategy; static; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  public
    function GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; override;
  end;

  { TGoldenRatioGrowStrategy 黄金比例增长 }
  TGoldenRatioGrowStrategy = class(TCalcGrowStrategy)
  protected
    function DoCalc(aCurrentSize: SizeUInt): SizeUInt; override;
  private
    class var FGlobal: TGoldenRatioGrowStrategy;
    class destructor Destroy;
  public
    class function GetGlobal: TGoldenRatioGrowStrategy; static; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  end;

  { TAlignedWrapperStrategy 对齐包装增长策略 }
  TAlignedWrapperStrategy = class(TGrowthStrategy)
  private
    FGrowStrategy: TGrowthStrategy;
    FAlignSize: SizeUInt;

    function GetGrowStrategy: TGrowthStrategy;
    function GetAlignSize: SizeUInt;
  public
    const
      DEFAULT_ALIGN_SIZE    = 64;
  public
    constructor Create(aGrowStrategy: TGrowthStrategy; aAlignSize: SizeUInt);
    function GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; override;

    property GrowStrategy: TGrowthStrategy read GetGrowStrategy;
    property AlignSize: SizeUInt read GetAlignSize;
  end;

  { TExactGrowStrategy 精确增长策略 }
  TExactGrowStrategy = class(TGrowthStrategy)
  private
    class var FGlobal: TExactGrowStrategy;
    class destructor Destroy;
  public
    class function GetGlobal: TExactGrowStrategy; static; {$IFDEF FAFAFA_COLLECTIONS_INLINE} inline;{$ENDIF}
  public
    function GetGrowSize(aCurrentSize, aRequiredSize: SizeUInt): SizeUInt; override;
  end;



  { IVec 向量接口 }
  generic IVec<T> = interface(specialize IArray<T>)
  ['{C205D988-F671-4E47-8573-9AF2C85AC749}']

    {**
     * GetCapacity
     *
     * @desc 获取向量的容量
     *
     * @return 返回向量的容量
     *}
    function GetCapacity: SizeUint;

    {**
     * SetCapacity
     *
     * @desc 设置向量的容量
     *
     * @params
     *   aCapacity 要设置的容量
     *
     * @remark 如果失败会抛出异常
     *}
    procedure SetCapacity(aCapacity: SizeUint);
  end;




implementation

end.